# Test Users - Detailed Documentation

<!-- Information sourced from src/DataFixtures/Tests/TestUserFixtures.php -->

This document provides detailed information about all test users available in the TedaConnect Marketplace application. All test users use the password: `password123`

## Standard User Types

### Licensee Users
Users who can license technology from licensors.

#### Complete Licensee User
- **Email:** `<EMAIL>`
- **Name:** Tommy the Test Licensee
- **Scope:** Licensee
- **Status:** Verified
- **Description:** Complete licensee user with all required data
- **Use Case:** Testing standard licensee functionality

#### Licensee Missing Company
- **Email:** `<EMAIL>`
- **Name:** Licensee Company Missing
- **Scope:** Licensee
- **Status:** Verified
- **Description:** Licensee user missing company information
- **Use Case:** Testing behavior when company data is incomplete

#### Licensee Missing Profile
- **Email:** `<EMAIL>`
- **Name:** Licensee Profile Missing
- **Scope:** Licensee
- **Status:** Verified
- **Description:** Licensee user missing profile information
- **Use Case:** Testing behavior when profile data is incomplete

### Licensor Users
Users who can offer technology licenses to others.

#### Complete Licensor User
- **Email:** `<EMAIL>`
- **Name:** Leo the Test Licensor
- **Scope:** Licensor
- **Status:** Verified
- **Company:** test-licensor-company
- **Description:** Complete licensor user with all required data
- **Use Case:** Testing standard licensor functionality

#### Licensor Missing Company
- **Email:** `<EMAIL>`
- **Name:** Licensor Company Missing
- **Scope:** Licensee (Note: scope mismatch for testing)
- **Status:** Verified
- **Description:** Licensor user missing company information
- **Use Case:** Testing behavior when company data is incomplete and scope mismatch scenarios

#### Licensor Missing Profile
- **Email:** `<EMAIL>`
- **Name:** Licensor Profile Missing
- **Scope:** Licensor
- **Status:** Verified
- **Description:** Licensor user missing profile information
- **Use Case:** Testing behavior when profile data is incomplete

### Investor Users
Users who can invest in technology opportunities.

#### Complete Investor User
- **Email:** `<EMAIL>`
- **Name:** Ivan the Test Investor
- **Scope:** Investor
- **Status:** Verified
- **Company:** test-investor-company
- **Description:** Complete investor user with all required data
- **Use Case:** Testing standard investor functionality

#### Investor Missing Company
- **Email:** `<EMAIL>`
- **Name:** Investor Company Missing
- **Scope:** Licensee (Note: scope mismatch for testing)
- **Status:** Verified
- **Description:** Investor user missing company information
- **Use Case:** Testing behavior when company data is incomplete and scope mismatch scenarios

#### Investor Missing Profile
- **Email:** `<EMAIL>`
- **Name:** Investor Profile Missing
- **Scope:** Licensee (Note: scope mismatch for testing)
- **Status:** Verified
- **Description:** Investor user missing profile information
- **Use Case:** Testing behavior when profile data is incomplete and scope mismatch scenarios

## Special Test Cases

### Unverified User
- **Email:** `<EMAIL>`
- **Name:** Username Unverified
- **Scope:** None
- **Status:** Not verified
- **Description:** User account that hasn't been verified yet
- **Use Case:** Testing email verification flows and access restrictions for unverified users

### Missing Scope User
- **Email:** `<EMAIL>`
- **Name:** Scope Missing
- **Scope:** None
- **Status:** Verified
- **Description:** Verified user without an assigned scope
- **Use Case:** Testing behavior when users don't have a defined role/scope in the system

## Administrative Users

### Admin User
- **Email:** `<EMAIL>`
- **Name:** Awesome Admin
- **Role:** Admin
- **Status:** Verified
- **Description:** Administrative user with admin privileges
- **Use Case:** Testing administrative functionality and access controls

### Owner User
- **Email:** `<EMAIL>`
- **Name:** Awesome Owner
- **Role:** Owner
- **Status:** Verified
- **Description:** Owner user with highest level privileges
- **Use Case:** Testing owner-level functionality and system-wide access

## Usage Notes

### General Information
- **Common Password:** All test users share the same password: `password123`
- **Purpose:** These users are created by the test fixtures and are intended for development and testing purposes only
- **Data Integrity:** Some users intentionally have missing data or mismatched scopes to test edge cases and error handling
- **Loading:** The fixtures are loaded when running tests or when specifically loading the 'tests' fixture group

### Testing Scenarios
- **Complete Users:** Use `<EMAIL>`, `<EMAIL>`, or `<EMAIL>` for testing standard workflows
- **Missing Data:** Use users with "missing" in their email for testing incomplete profile scenarios
- **Scope Mismatches:** Several users have intentional scope mismatches to test error handling
- **Verification:** Use `<EMAIL>` to test email verification flows
- **Admin Functions:** Use `<EMAIL>` or `<EMAIL>` for testing administrative features

### Security Considerations
- These test users can only be used in development and testing environments
- The simple password is intentional for testing but should not be used as a reference for production password policies
