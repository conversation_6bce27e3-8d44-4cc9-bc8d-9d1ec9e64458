{# @var brand \App\Entity\Brand #}
<div class="contents" data-testid="brand-list-item">
    <div class="card border border-stroke flex flex-col gap-4">
        <div class="font-semibold text-sm text-primary-text flex flex-row justify-between">
            <span>{{ brand.name }}</span>
            <span>By {{ brand.licensor.company.name }}
                <img
                    class="ml-2 inline"
                    src="{{ brand.licensor.company.logo }}"
                    width="28px"
                    height="28px"
                    title="{{ brand.licensor.company.name }}"
                    alt="{{ brand.licensor.company.name }}"
                />
            </span>
        </div>
        <a class="w-full" href="{{ url('app_licensor_brand_details', {slug: brand.slug}) }}">
            <img src="{{ brand.heroImage }}" alt="{{ brand.name }}"/>
        </a>

        <div class="mt-8">
            <twig:layout:badge-list
                    label="IP Categories"
                    itemClass="badge bg-brand-light text-primary-text/80"
                    values="{{ brand.ipCategories }}"
                    hideAfter="4"
            />
        </div>
        <div class="my-8">
            <twig:layout:badge-list
                    label="Territories"
                    itemClass="badge bg-brand-light text-primary-text/80"
                    values="{{ brand.licensor.territories }}"
                    hideAfter="4"
            />
        </div>
        <div class="mt-auto">
            <twig:layout:dialog buttonText="Explore opportunities" buttonClass="btn-secondary w-full">
                <h2>Brand opportunities</h2>
                <twig:pages:licensor:brand-opportunity-list brandId="{{ brand.id }}" loading="lazy"/>
            </twig:layout:dialog>
        </div>
    </div>
</div>
