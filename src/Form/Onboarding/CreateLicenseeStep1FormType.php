<?php

declare(strict_types=1);

namespace App\Form\Onboarding;

use App\Entity\IPCategory;
use App\Entity\Licensee;
use App\Form\Type\TargetAudienceType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\DataMapperInterface;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class CreateLicenseeStep1FormType extends AbstractType implements DataMapperInterface
{

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Licensee::class,
        ]);
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('ipCategoryInterests', EntityType::class, [
            'required' => true,
            'multiple' => true,
            'autocomplete' => true,
            'class' => IPCategory::class,
            'choice_label' => 'name',
            'label' => 'Brand category',
            'help' => 'Choose property categories you would like to work with.',
        ]);

        $builder->add('targetAudience', TargetAudienceType::class, [
            'label' => 'Target audience',
            'help' => 'Who is your brand primarily intended for?',
            'mapped' => false,
        ]);

        $builder->add('submit', SubmitType::class, [
            'label' => 'Continue',
            'attr' => [
                'class' => 'btn btn-primary',
            ],
        ]);

        $builder->setDataMapper($this);
    }

    /**
     * @param Licensee $viewData
     */
    public function mapDataToForms(mixed $viewData, \Traversable $forms): void
    {
        /** @var FormInterface[] $forms */
        $forms = iterator_to_array($forms);

        $forms['ipCategoryInterests']->setData($viewData->ipCategoryInterests);
        $forms['targetAudience']->setData(
            [
                'ageGroups' => $viewData->targetAudienceAgeGroups,
                'genders' => $viewData->targetAudienceGenders,
            ]
        );
    }

    /**
     * @param Licensee $viewData
     */
    public function mapFormsToData(\Traversable $forms, mixed &$viewData): void
    {
        /** @var FormInterface[] $forms */
        $forms = iterator_to_array($forms);

        $viewData->ipCategoryInterests = $forms['ipCategoryInterests']->getData();
        $viewData->targetAudienceAgeGroups = $forms['targetAudience']['ageGroups']->getData();
        $viewData->targetAudienceGenders = $forms['targetAudience']['genders']->getData();
    }

}
