@layer components {
    form {
        label {
            @apply text-neutral text-sm font-medium;

            .help-text {
                @apply inline-block float-right;
            }
        }

        legend {
            @apply text-neutral text-lg font-semibold;
        }

        fieldset {
            @apply mb-2;
        }

        input[type="text"],
        input[type="url"],
        input[type="email"],
        input[type="password"],
        input[type="search"],
        input[type="date"],
        textarea,
        select,
        .input-label {
            @apply w-full mt-1 border border-stroke rounded-md px-3 py-2 focus:ring-primary focus:border-primary;
        }

        .input-label {
            @apply text-white bg-black cursor-pointer text-center;
            &:hover {
                @apply text-white/80;
            }
        }

        select[multiple] {
            @apply h-32;
        }

        input[type="checkbox"] {
            @apply w-5 h-5 mt-1 mr-3 border border-stroke rounded-md px-3 py-2 focus:ring-primary focus:border-primary float-left;
        }

        .form-group {
            @apply flex flex-col gap-2;
        }
    }

    .form-horizontal form,
    form .form-horizontal {
        label {
            @apply mb-0;
        }

        input[type="text"],
        input[type="url"],
        input[type="email"],
        input[type="password"],
        input[type="search"],
        input[type="date"],
        input[type="file"],
        textarea,
        select,
        .input-label {
            @apply w-fit mt-0 mx-1 px-3 py-1;
        }
    }
}
