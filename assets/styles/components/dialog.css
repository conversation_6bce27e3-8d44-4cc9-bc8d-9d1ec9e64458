@layer base {
    dialog {
        @apply bg-page-background rounded-2xl border border-stroke shadow-xs p-4 m-auto backdrop:bg-(--color-page-background)/65 h-fit overflow-visible;
    }

    @keyframes fade-in {
        0% {
            opacity: 0;
        }
        100% {
            opacity: 1;
        }
    }

    @keyframes fade-out {
        0% {
            opacity: 1;
        }
        100% {
            opacity: 0;
        }
    }

    /* Add animations */
    dialog[data-dialog-target="dialog"][open] {
        animation: fade-in 200ms forwards;
    }

    dialog[data-dialog-target="dialog"][closing] {
        animation: fade-out 200ms forwards;
    }
}
